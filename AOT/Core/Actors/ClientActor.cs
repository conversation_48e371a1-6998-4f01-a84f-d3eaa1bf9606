using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Threading.Tasks;
using Akka.Actor;
using Akka.IO;
using HeroYulgang.Core.Network;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using RxjhServer;
using RxjhServer.Database;
using RxjhServer.HelperTools;

namespace HeroYulgang.Core.Actors
{
    /// <summary>
    /// Actor xử lý kết nối của một client cụ thể - đã hợp nhất với PacketHandlerActor
    /// </summary>
    [DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.NonPublicConstructors)]
    public class ClientActor : ReceiveActor
    {
        private readonly IActorRef _connection;
        private readonly ClientSession _session;
        private Players? _player;
        private readonly ActorNetState _actorNetState;
        private readonly Dictionary<PacketType, Action<IActorRef, ClientSession, Packet>> _handlers = [];
        private readonly ActorSelection _tcpManager;

        // Buffer để xử lý packet gộp
        private readonly PacketBuffer _packetBuffer = new();

        public ClientActor(IActorRef connection, ClientSession session)
        {
            _connection = connection;
            _session = session;

            // Lấy reference đến TcpManager - sử dụng ActorSelection trực tiếp
            _tcpManager = Context.ActorSelection("/user/tcpManager");

            // Tạo ActorNetState cho kết nối này
            _actorNetState = PlayerNetworkManager.CreateActorNetState(_connection, _session.SessionId, _session.RemoteEndPoint);

            // Đăng ký packet handlers
            RegisterHandlers();

            // Định nghĩa các message handler
            Receive<Tcp.Received>(HandleReceivedAsync);
            Receive<Tcp.ConnectionClosed>(HandleConnectionClosed);
            Receive<SetPlayerReference>(SetPlayerReference);
            Receive<SetPlayerReferenceAndLogin>(SetPlayerReferenceAndLogin);
        }

        private void RegisterHandlers()
        {
            _handlers[PacketType.Valid1375] = HandlePreLogin;
            _handlers[PacketType.Login] = HandleLoginSync;
        }

        private void SetPlayerReference(SetPlayerReference message)
        {
            _player = message.Player;

            // Thiết lập player context trực tiếp trong ClientActor
            if (_player != null && _actorNetState != null)
            {
                Logger.Instance.Debug($"Player ID {_player.AccountID} (SessionID: {_player.SessionID}) đã kết nối {_actorNetState.SessionID} ");
                // Cập nhật Client của Player để sử dụng ActorNetState
                PlayerNetworkManager.UpdatePlayerClient(_player, _actorNetState);

                Logger.Instance.Debug($"Đã thiết lập player context cho người chơi {_player.CharacterName}");
            }
        }

        private void SetPlayerReferenceAndLogin(SetPlayerReferenceAndLogin message)
        {
            try
            {
                _player = message.Player;

                // Thiết lập player context cho PacketHandlerActor
                if (_player != null && _actorNetState != null)
                {
                    // Đồng bộ SessionID giữa Player và ActorNetState
                    // Tránh sử dụng reflection để tương thích với Hot Reload
                    try
                    {
                        // Kiểm tra xem có method public để set SessionID không
                        if (_player.SessionID != _actorNetState.SessionID)
                        {
                            Logger.Instance.Warning($"SessionID mismatch: Player={_player.SessionID}, ActorNetState={_actorNetState.SessionID}");
                            // Có thể cần tạo method public trong PlayersBes để set SessionID
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance.Warning($"Không thể đồng bộ SessionID: {ex.Message}");
                    }

                    Logger.Instance.Debug($"Player ID {_player.AccountID} (SessionID: {_player.SessionID}) đã kết nối {_actorNetState.SessionID}");

                    // Kiểm tra xem player đã đăng nhập chưa
                    if (World.allConnectedChars.TryGetValue(_player.SessionID, out var existingPlayer))
                    {
                        Logger.Instance.Warning($"Player {message.Username} đã có trong World với SessionID {_player.SessionID}");

                        // Ngắt kết nối cũ nếu có
                        try
                        {
                            existingPlayer.Client?.Dispose();
                            World.allConnectedChars.Remove(_player.SessionID);
                            Logger.Instance.Debug($"Đã ngắt kết nối cũ cho player {message.Username}");
                        }
                        catch (Exception ex)
                        {
                            Logger.Instance.Error($"Lỗi khi ngắt kết nối cũ: {ex.Message}");
                        }
                    }

                    // Cập nhật Client của Player để sử dụng ActorNetState
                    PlayerNetworkManager.UpdatePlayerClient(_player, _actorNetState);

                    Logger.Instance.Debug($"Đã thiết lập player context cho người chơi {_player.AccountID}");

                    // Xử lý đăng nhập
                    try
                    {
                        Task.Run(() => _player?.KetNoi_DangNhap(message.LoginData, message.LoginData.Length));
                       // _player?.KetNoi_DangNhap(message.LoginData, message.LoginData.Length);
                        Logger.Instance.Debug($"Người dùng {message.Username} đã gọi KetNoi_DangNhap thành công");
                        message.Session.IsAuthenticated = true;
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance.Error($"Lỗi khi xử lý đăng nhập cho {message.Username}: {ex.Message}");
                        Logger.Instance.Error($"Stack trace: {ex.StackTrace}");

                        // Ngắt kết nối nếu đăng nhập thất bại
                        _connection.Tell(Tcp.Close.Instance);
                    }
                }
                else
                {
                    Logger.Instance.Error($"SetPlayerReferenceAndLogin: _player hoặc _actorNetState là null");
                    _connection.Tell(Tcp.Close.Instance);
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi trong SetPlayerReferenceAndLogin cho {message.Username}: {ex.Message}");
                Logger.Instance.Error($"Stack trace: {ex.StackTrace}");
                _connection.Tell(Tcp.Close.Instance);
            }
        }
        
         private void HandleReceivedAsync(Tcp.Received received)
        {
            try
            {
                _session.UpdateActivity();
                byte[] data = received.Data.ToArray();

                // Debug logging cho raw packet
                //Logger.Instance.Debug($"[DEBUG] HandleReceivedAsync - SessionID: {_session.SessionId}, Raw packet length: {data.Length}");

                // Thêm dữ liệu vào buffer
                _packetBuffer.AddData(data);

                // Xử lý tất cả các packet hoàn chỉnh trong buffer
                ProcessPacketsFromBuffer();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý dữ liệu nhận được từ client {_session.SessionId}: {ex.Message}");
                Logger.Instance.Error($"Stack trace: {ex.StackTrace}");

                // Xóa buffer khi có lỗi nghiêm trọng
                _packetBuffer.Clear();
            }
        }

        /// <summary>
        /// Xử lý tất cả các packet hoàn chỉnh từ buffer
        /// </summary>
        private void ProcessPacketsFromBuffer()
        {
            // Trích xuất tất cả packet hoàn chỉnh
            var packets = _packetBuffer.ExtractCompletePackets();

            //Logger.Instance.Debug($"[DEBUG] Extracted {packets.Count} complete packets from buffer");

            foreach (var packetData in packets)
            {
                try
                {
                    // Giải mã packet
                    byte[] decryptedData = Utils.Crypto.DecryptPacket(packetData);

                    // Debug logging cho decrypted packet
                    if (decryptedData.Length >= 10)
                    {
                        int opcode = BitConverter.ToInt16(decryptedData, 8);
                        //Logger.Instance.Debug($"[DEBUG] Processing packet opcode: {opcode}");
                    }

                    // Xử lý packet
                    if (_player != null)
                    {
                        // Xử lý packet cho player đã đăng nhập
                        ProcessPlayerPacketData(decryptedData, decryptedData.Length).GetAwaiter().GetResult();
                    }
                    else
                    {
                        // Xử lý packet cho client chưa đăng nhập (login packets)
                        ProcessPacketSync(decryptedData);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Instance.Error($"Lỗi khi xử lý packet từ buffer: {ex.Message}");
                    Logger.Instance.Error($"Stack trace: {ex.StackTrace}");

                    // Tiếp tục xử lý packet tiếp theo thay vì dừng hoàn toàn
                }
            }
        }


        /// <summary>
        /// Xử lý packet cho client chưa đăng nhập (sync version)
        /// </summary>
        private void ProcessPacketSync(byte[] data)
        {
            try
            {
                var packet = Packet.Parse(data, data.Length);

                // Ghi log gói tin đã phân tích
                PacketLogger.LogPacket(_session.SessionId, packet, true);

                if (_handlers.TryGetValue(packet.Type, out var handler))
                {
                    // Gọi handler trực tiếp - không sử dụng async
                    handler(_connection, _session, packet);
                }
                else
                {
                    Logger.Instance.Warning($"1 Không có handler cho gói tin loại {packet.Type} từ session {_session.SessionId}");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý gói tin từ session {_session.SessionId}: {ex.Message}");
            }
        }

        private void HandlePreLogin(IActorRef connection, ClientSession session, Packet packet)
        {
            Handle1375(packet.Data, packet.Data.Length);
        }

        /// <summary>
        /// Xử lý gói tin đăng nhập (sync version)
        /// </summary>
        private void HandleLoginSync(IActorRef connection, ClientSession session, Packet packet)
        {
            try
            {
                var reader = new BinaryReader(new MemoryStream(packet.Data));
                reader.BaseStream.Seek(12, SeekOrigin.Begin);
                byte[] userNameByte = reader.ReadBytes(14);
                string username = System.Text.Encoding.Default.GetString(userNameByte).Trim().Replace("\0", String.Empty);
                if (username.Length == 0)
                {
                    connection.Tell(Tcp.Close.Instance);
                    return;
                }

                var account = AccountDb.FindAccount(username).Result;
                if (account == null)
                {
                    connection.Tell(Tcp.Close.Instance);
                    return;
                }
                Logger.Instance.Debug($"Yêu cầu đăng nhập từ {username}");
                // Tạo player với thông tin cơ bản
                var player = new Players
                {
                    AccountID = username,
                    LanIp = username,
                    SessionID = session.SessionId
                };

                session.IsAuthenticated = false;
                session.AccountId = username;

                // Chuẩn bị data cho đăng nhập
                var dataX = new byte[packet.Data.Length - 2];
                Buffer.BlockCopy(packet.Data, 0, dataX, 0, 6);
                Buffer.BlockCopy(packet.Data, 8, dataX, 6, packet.Data.Length - 8);

                // Gửi message với cả player reference và login data - sử dụng Self trực tiếp
                Self.Tell(new SetPlayerReferenceAndLogin(player, dataX, session, username));
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý gói tin đăng nhập: {ex.Message}");

                // Gửi phản hồi thất bại - không sử dụng async
                try
                {
                    byte[] responseData = BitConverter.GetBytes(0); // 0 = thất bại
                    var response = new Packet(PacketType.Login, responseData);
                    SendPacketSync(connection, response.ToByteArray());
                }
                catch (Exception sendEx)
                {
                    Logger.Instance.Error($"Lỗi khi gửi phản hồi thất bại: {sendEx.Message}");
                }
            }
        }

        /// <summary>
        /// Xử lý packet data cho player - được chuyển từ PacketHandlerActor
        /// </summary>
        private async Task ProcessPlayerPacketData(byte[] data, int length)
        {
            if (_player == null)
            {
                Logger.Instance.Warning("ProcessPlayerPacketData: _player là null");
                return;
            }

            // Kiểm tra _player?.Client trước khi sử dụng
            if (_player?.Client == null)
            {
                Logger.Instance.Warning($"ProcessPlayerPacketData: _player?.Client là null cho player {_player?.AccountID}");
                return;
            }

            // Lấy packet type từ data
            int packetType = BitConverter.ToInt16(data, 8);

            // Debug logging cho packet type
            Logger.Instance.Debug($"[{Self.Path.Name}] [{packetType}] - {_player.AccountID} PacketProcessing");

            try
            {
                // cut byte 6-7 from data
                var oldPacket = new byte[length - 2];
                Buffer.BlockCopy(data, 0, oldPacket, 0, 6);
                Buffer.BlockCopy(data, 8, oldPacket, 6, length - 8);

                // Kiểm tra xem người chơi đã kết nối chưa
                if (!World.allConnectedChars.TryGetValue(_player.SessionID, out var _))
                {
                    // Người chơi chưa kết nối, xử lý pre-login packets
                    HandlePreLoginPackets(packetType, oldPacket, oldPacket.Length);
                    return;
                }

                // Xử lý heartbeat riêng
                if (packetType == 176)
                {
                    _player?.Phat_Hien_Nhip_Tim(oldPacket, oldPacket.Length);
                    return;
                }

                // Kiểm tra kết nối thành công
                if (!_player._connectionSucceeded)
                {
                    Logger.Instance.Warning($"[{Self.Path.Name}] Connection not succeeded cho player {_player?.AccountID}, packet type {packetType}");
                    throw new Exception("Connection not succeeded");
                }

                // Xử lý các loại packet khác
               // Logger.Instance.Debug($"ClientActor [{Self.Path.Name}] Xử lý game packet {packetType} cho player {_player?.AccountID}");
                await HandleGamePacketsSync(packetType, oldPacket, oldPacket.Length);
            }
            catch (Exception ex)
            {
                // Kiểm tra _player?.Client trước khi truy cập thuộc tính
                string clientInfo = "unknown";
                string worldId = "unknown";

                try
                {
                    if (_player?.Client != null)
                    {
                        worldId = _player.Client.SessionID.ToString();
                        clientInfo = _player.Client.ToString();
                    }
                }
                catch (Exception clientEx)
                {
                    Logger.Instance.Error($"Lỗi khi truy cập thông tin client: {clientEx.Message}");
                }

                LogHelper.WriteLine(LogLevel.Error, $"Manage Packet() Lỗi tại case: [{packetType}]-[{worldId}]-[{clientInfo}] [{ex}]");
                Logger.Instance.Error($"Stack trace: {ex.StackTrace}");

                // Kiểm tra trước khi dispose
                try
                {
                    _player?.Client?.Dispose();
                }
                catch (Exception disposeEx)
                {
                    Logger.Instance.Error($"Lỗi khi dispose client: {disposeEx.Message}");
                }

                LogHelper.WriteLine(LogLevel.Error, $"Disconnected![{_player?.AccountID}]-[{_player?.CharacterName}][Mã dis 10]");
            }
        }

        /// <summary>
        /// Xử lý các packet trước khi đăng nhập
        /// </summary>
        private void HandlePreLoginPackets(int packetType, byte[] data, int length)
        {
            switch (packetType)
            {
                case 20:
                    _player?.CreateCharacter(data, length);
                    break;
                case 16:
                    _player?.GetAListOfPeople(data, length);
                    break;
                case 1:
                    _player?.KetNoi_DangNhap(data, length);
                    break;
                case 3:
                    _player?.DangXuat(data, length);
                    break;
                case 5:
                    _player?.CharacterLogin(data, length);
                    break;
                case 143:
                    _player?.Display();
                    break;
                case 56:
                    _player?.KiemTraNhanVat_CoTonTaiHayKhong(data, length);
                    break;
                case 30:
                    _player?.XoaBoNhanVat(data, length);
                    break;
                case 836:
                    _player?.XacMinhThongTinDangNhapId(data, length);
                    break;
                case 218:
                case 211:
                    _player?.ChangeLineVerification(data, length);
                    break;
                case 16666:
                    _player?.IsAttackConfirmation(data, length);
                    break;
                case 5638:
                case 8212:
                    _player?.VersionVerification(data, length);
                    break;
                case 1375:
                    Handle1375(data, length);
                    break;
            }
        }


        private void Handle1375(byte[] data, int length)
        {
            try
            {
                var array = Converter.HexStringToByte("aa550000d5040000600527010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000200010055aa");
                Buffer.BlockCopy(BitConverter.GetBytes(_session.SessionId), 0, array, 4, 2);
                _actorNetState.Send(array, length);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, "Handle1375 Error "+ ex.Message);
            }
        }

        /// <summary>
        /// Xử lý các packet trong game
        /// </summary>
        private async Task HandleGamePacketsSync(int packetType, byte[] data, int length)
        {
            // Gọi ManagePacket mà không await để tránh vấn đề ActorContext
            if (_player == null)
            {
                // Disconnect Client
                _connection.Tell(Tcp.Close.Instance);
                return;
            }
            await _player.ManagePacket(data, data.Length);
        }

        /// <summary>
        /// Helper method để gửi gói tin (sync version)
        /// </summary>
        private void SendPacketSync(IActorRef connection, byte[] data)
        {
            try
            {
                // Gửi gói tin đến TcpManagerActor sử dụng reference đã lưu
                _tcpManager.Tell(new SendPacket(connection, data));

                // Ghi log gói tin gửi đi
                if (data.Length >= 2)
                {
                    PacketLogger.LogOutgoingPacket(_session.SessionId, data);
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi gửi packet: {ex.Message}");
            }
        }

        /// <summary>
        /// Helper method để gửi gói tin (async version)
        /// </summary>
        private Task SendPacketAsync(IActorRef connection, byte[] data)
        {
            SendPacketSync(connection, data);
            return Task.CompletedTask;
        }

        private void HandleConnectionClosed(Tcp.ConnectionClosed message)
        {
            // Kết nối đã đóng, dừng actor
            Context.Stop(Self);
        }
    }

    /// <summary>
    /// Message để thiết lập tham chiếu đến đối tượng Player
    /// </summary>
    public class SetPlayerReference(Players player)
    {
        public Players Player { get; } = player;
    }

    /// <summary>
    /// Message để thiết lập tham chiếu đến đối tượng Player và xử lý đăng nhập
    /// </summary>
    public class SetPlayerReferenceAndLogin(Players player, byte[] loginData, ClientSession session, string username)
    {
        public Players Player { get; } = player;
        public byte[] LoginData { get; } = loginData;
        public ClientSession Session { get; } = session;
        public string Username { get; } = username;
    }

    /// <summary>
    /// Message yêu cầu xử lý gói tin
    /// </summary>
    public class ProcessPacket
    {
        public IActorRef Connection { get; }
        public ClientSession Session { get; }
        public byte[] Data { get; }

        public ProcessPacket(IActorRef connection, ClientSession session, byte[] data)
        {
            Connection = connection;
            Session = session;
            Data = data;
        }
    }
}
